#!/usr/bin/env python3
"""
Create Simple, Readable CSV Files

Transform the cryptic acronym-filled data into simple, human-readable format
with clear names and immediate insights.
"""

import pandas as pd
import os

def create_simple_readable_csv():
    """Create simple, readable CSV files from the comprehensive data"""
    
    # Load the comprehensive data
    input_file = 'exports_comprehensive/macro_indicators_comprehensive.csv'
    output_dir = 'simple_data'
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    print("📊 Creating Simple, Readable CSV Files")
    print("=" * 50)
    
    # Load data
    df = pd.read_csv(input_file, index_col='date', parse_dates=True)
    
    # Define simple, readable column mappings
    readable_columns = {
        # Interest Rates & Fed Policy
        'FEDFUNDS': 'Fed_Interest_Rate_%',
        'DGS10': '10_Year_Treasury_%',
        'DGS2': '2_Year_Treasury_%',
        'T10Y2Y': 'Yield_Curve_Spread_%',
        'MORTGAGE30US': '30_Year_Mortgage_Rate_%',
        
        # Employment & Jobs
        'UNRATE': 'Unemployment_Rate_%',
        'PAYEMS': 'Total_Jobs_Millions',
        'ICSA': 'Weekly_Jobless_Claims',
        'CIVPART': 'Labor_Participation_Rate_%',
        
        # Inflation & Prices
        'CPIAUCSL': 'Consumer_Price_Index',
        'CPILFESL': 'Core_Inflation_Index',
        'T5YIE': '5_Year_Inflation_Expectation_%',
        'T10YIE': '10_Year_Inflation_Expectation_%',
        
        # Economic Growth
        'GDP': 'GDP_Billions_USD',
        'GDPC1': 'Real_GDP_Billions_USD',
        'INDPRO': 'Industrial_Production_Index',
        'UMCSENT': 'Consumer_Confidence_Index',
        
        # Housing Market
        'HOUST': 'Housing_Starts_Thousands',
        'MSPUS': 'Median_Home_Price_USD',
        'CSUSHPINSA': 'Home_Price_Index',
        
        # Stock Market
        'SP500': 'S&P_500_Index',
        'VIXCLS': 'Market_Fear_Index_VIX',
        
        # Commodities
        'DCOILWTICO': 'Oil_Price_USD_per_Barrel',
        'DHHNGSP': 'Natural_Gas_Price_USD',
        
        # Money & Banking
        'M2SL': 'Money_Supply_M2_Billions',
    }
    
    # 1. Create Main Economic Dashboard
    print("1️⃣ Creating Economic Dashboard...")
    
    # Select key indicators for dashboard
    dashboard_cols = [
        'Fed_Interest_Rate_%',
        '10_Year_Treasury_%', 
        'Yield_Curve_Spread_%',
        'Unemployment_Rate_%',
        'Consumer_Price_Index',
        'GDP_Billions_USD',
        'Consumer_Confidence_Index',
        'S&P_500_Index',
        'Market_Fear_Index_VIX',
        'Oil_Price_USD_per_Barrel'
    ]
    
    # Create dashboard dataframe
    dashboard_df = pd.DataFrame(index=df.index)
    
    for new_name, old_name in readable_columns.items():
        if old_name in dashboard_cols and new_name in df.columns:
            dashboard_df[old_name] = df[new_name]
    
    # Add calculated fields for immediate insights
    dashboard_df['Date'] = dashboard_df.index.strftime('%Y-%m-%d')
    
    # Calculate recession probability based on yield curve
    if 'Yield_Curve_Spread_%' in dashboard_df.columns:
        dashboard_df['Recession_Risk'] = dashboard_df['Yield_Curve_Spread_%'].apply(
            lambda x: 'HIGH' if pd.notna(x) and x < 0 else 
                     'MEDIUM' if pd.notna(x) and x < 0.5 else 
                     'LOW' if pd.notna(x) else 'UNKNOWN'
        )
    
    # Calculate market sentiment
    if 'Market_Fear_Index_VIX' in dashboard_df.columns:
        dashboard_df['Market_Sentiment'] = dashboard_df['Market_Fear_Index_VIX'].apply(
            lambda x: 'PANIC' if pd.notna(x) and x > 30 else
                     'FEAR' if pd.notna(x) and x > 20 else
                     'CALM' if pd.notna(x) and x < 15 else
                     'NORMAL' if pd.notna(x) else 'UNKNOWN'
        )
    
    # Reorder columns for readability
    cols_order = ['Date'] + [col for col in dashboard_df.columns if col != 'Date']
    dashboard_df = dashboard_df[cols_order]
    
    # Sort by date (most recent first)
    dashboard_df = dashboard_df.sort_index(ascending=False)
    
    # Export dashboard
    dashboard_df.to_csv(f'{output_dir}/economic_dashboard.csv', index=False)
    print(f"   ✅ Economic Dashboard -> economic_dashboard.csv ({len(dashboard_df)} rows)")
    
    # 2. Create Interest Rates Summary
    print("2️⃣ Creating Interest Rates Summary...")
    
    rates_df = pd.DataFrame(index=df.index)
    rates_df['Date'] = rates_df.index.strftime('%Y-%m-%d')
    
    rate_columns = {
        'FEDFUNDS': 'Fed_Rate_%',
        'DGS2': '2_Year_Treasury_%',
        'DGS10': '10_Year_Treasury_%',
        'MORTGAGE30US': '30_Year_Mortgage_%',
        'T10Y2Y': 'Yield_Curve_Spread_%'
    }
    
    for old_col, new_col in rate_columns.items():
        if old_col in df.columns:
            rates_df[new_col] = df[old_col]
    
    # Add interpretations
    if 'Yield_Curve_Spread_%' in rates_df.columns:
        rates_df['Curve_Status'] = rates_df['Yield_Curve_Spread_%'].apply(
            lambda x: 'INVERTED (Recession Warning)' if pd.notna(x) and x < 0 else
                     'FLAT (Caution)' if pd.notna(x) and x < 0.5 else
                     'NORMAL (Healthy)' if pd.notna(x) else 'UNKNOWN'
        )
    
    rates_df = rates_df.sort_index(ascending=False)
    rates_df.to_csv(f'{output_dir}/interest_rates.csv', index=False)
    print(f"   ✅ Interest Rates -> interest_rates.csv ({len(rates_df)} rows)")
    
    # 3. Create Employment Summary
    print("3️⃣ Creating Employment Summary...")
    
    employment_df = pd.DataFrame(index=df.index)
    employment_df['Date'] = employment_df.index.strftime('%Y-%m-%d')
    
    employment_columns = {
        'UNRATE': 'Unemployment_Rate_%',
        'PAYEMS': 'Total_Jobs_Millions',
        'ICSA': 'Weekly_Jobless_Claims',
        'CIVPART': 'Labor_Participation_%'
    }
    
    for old_col, new_col in employment_columns.items():
        if old_col in df.columns:
            employment_df[new_col] = df[old_col]
    
    # Add job market health indicator
    if 'Unemployment_Rate_%' in employment_df.columns:
        employment_df['Job_Market_Health'] = employment_df['Unemployment_Rate_%'].apply(
            lambda x: 'EXCELLENT' if pd.notna(x) and x < 4 else
                     'GOOD' if pd.notna(x) and x < 6 else
                     'WEAK' if pd.notna(x) and x < 8 else
                     'POOR' if pd.notna(x) else 'UNKNOWN'
        )
    
    employment_df = employment_df.sort_index(ascending=False)
    employment_df.to_csv(f'{output_dir}/employment_data.csv', index=False)
    print(f"   ✅ Employment Data -> employment_data.csv ({len(employment_df)} rows)")
    
    # 4. Create Market Summary
    print("4️⃣ Creating Market Summary...")
    
    market_df = pd.DataFrame(index=df.index)
    market_df['Date'] = market_df.index.strftime('%Y-%m-%d')
    
    market_columns = {
        'SP500': 'S&P_500_Index',
        'VIXCLS': 'Fear_Index_VIX',
        'DCOILWTICO': 'Oil_Price_USD',
        'DGS10': '10_Year_Treasury_%'
    }
    
    for old_col, new_col in market_columns.items():
        if old_col in df.columns:
            market_df[new_col] = df[old_col]
    
    # Add market sentiment
    if 'Fear_Index_VIX' in market_df.columns:
        market_df['Market_Mood'] = market_df['Fear_Index_VIX'].apply(
            lambda x: 'PANIC' if pd.notna(x) and x > 30 else
                     'NERVOUS' if pd.notna(x) and x > 20 else
                     'CALM' if pd.notna(x) and x < 15 else
                     'NORMAL' if pd.notna(x) else 'UNKNOWN'
        )
    
    market_df = market_df.sort_index(ascending=False)
    market_df.to_csv(f'{output_dir}/market_data.csv', index=False)
    print(f"   ✅ Market Data -> market_data.csv ({len(market_df)} rows)")
    
    # 5. Create Latest Snapshot
    print("5️⃣ Creating Latest Economic Snapshot...")
    
    # Get most recent data
    latest_data = {}
    latest_date = df.index.max()
    
    for old_col, new_col in readable_columns.items():
        if old_col in df.columns:
            latest_value = df[old_col].dropna().iloc[0] if not df[old_col].dropna().empty else None
            if latest_value is not None:
                latest_data[new_col] = latest_value
    
    # Create snapshot dataframe
    snapshot_df = pd.DataFrame([latest_data])
    snapshot_df.insert(0, 'Date', latest_date.strftime('%Y-%m-%d'))
    
    # Add interpretations
    if 'Fed_Interest_Rate_%' in snapshot_df.columns:
        fed_rate = snapshot_df['Fed_Interest_Rate_%'].iloc[0]
        if fed_rate > 5:
            snapshot_df['Fed_Policy_Stance'] = 'RESTRICTIVE (High Rates)'
        elif fed_rate > 2:
            snapshot_df['Fed_Policy_Stance'] = 'NEUTRAL'
        else:
            snapshot_df['Fed_Policy_Stance'] = 'ACCOMMODATIVE (Low Rates)'
    
    snapshot_df.to_csv(f'{output_dir}/latest_snapshot.csv', index=False)
    print(f"   ✅ Latest Snapshot -> latest_snapshot.csv (1 row)")
    
    # Calculate total size
    total_size = 0
    file_count = 0
    for root, dirs, files in os.walk(output_dir):
        for file in files:
            if file.endswith('.csv'):
                filepath = os.path.join(root, file)
                size = os.path.getsize(filepath)
                total_size += size
                file_count += 1
    
    print("\n" + "=" * 50)
    print("✅ SIMPLE, READABLE CSV FILES CREATED!")
    print("=" * 50)
    print(f"📁 Output directory: {output_dir}/")
    print(f"📊 Files created: {file_count}")
    print(f"💾 Total size: {total_size / (1024*1024):.2f} MB")
    
    print(f"\n📋 FILES CREATED:")
    print(f"  1. economic_dashboard.csv - Main economic indicators with insights")
    print(f"  2. interest_rates.csv - Fed policy and yield curve analysis")
    print(f"  3. employment_data.csv - Job market health indicators")
    print(f"  4. market_data.csv - Stock market and commodity prices")
    print(f"  5. latest_snapshot.csv - Most recent data snapshot")
    
    print(f"\n🎯 FEATURES:")
    print(f"  ✅ Clear, readable column names (no acronyms)")
    print(f"  ✅ Immediate insights and interpretations")
    print(f"  ✅ Most recent data first")
    print(f"  ✅ Ready for Excel, Google Sheets, or any analysis tool")

if __name__ == "__main__":
    os.chdir('macro_indicators')
    create_simple_readable_csv()
