#!/usr/bin/env python3
"""
Bitcoin-Macro Economic Analysis Framework

Shift focus from traditional stock market to Bitcoin, integrating macro economic
indicators with Bitcoin price data for comprehensive crypto-macro analysis.
"""

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
import os

def fetch_bitcoin_data():
    """Fetch Bitcoin price data from Yahoo Finance"""
    print("📈 Fetching Bitcoin Data...")
    
    # Fetch Bitcoin data (BTC-USD)
    btc = yf.Ticker("BTC-USD")
    
    # Get historical data (last 5 years for comprehensive analysis)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=5*365)  # 5 years
    
    btc_data = btc.history(start=start_date, end=end_date)
    
    if btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None
    
    # Clean and prepare Bitcoin data
    btc_df = pd.DataFrame()
    btc_df['Date'] = btc_data.index.strftime('%Y-%m-%d')
    btc_df['Bitcoin_Price_USD'] = btc_data['Close'].round(2)
    btc_df['Bitcoin_Volume'] = btc_data['Volume'].round(0)
    btc_df['Bitcoin_High_USD'] = btc_data['High'].round(2)
    btc_df['Bitcoin_Low_USD'] = btc_data['Low'].round(2)
    
    # Calculate Bitcoin metrics
    btc_df['Bitcoin_Daily_Change_%'] = btc_data['Close'].pct_change() * 100
    btc_df['Bitcoin_7D_Change_%'] = btc_data['Close'].pct_change(periods=7) * 100
    btc_df['Bitcoin_30D_Change_%'] = btc_data['Close'].pct_change(periods=30) * 100
    btc_df['Bitcoin_90D_Change_%'] = btc_data['Close'].pct_change(periods=90) * 100
    
    # Calculate volatility (30-day rolling)
    btc_df['Bitcoin_30D_Volatility_%'] = btc_data['Close'].pct_change().rolling(30).std() * np.sqrt(365) * 100
    
    # Calculate moving averages
    btc_df['Bitcoin_MA_50'] = btc_data['Close'].rolling(50).mean().round(2)
    btc_df['Bitcoin_MA_200'] = btc_data['Close'].rolling(200).mean().round(2)
    
    # Add trend indicators
    btc_df['Bitcoin_Trend'] = np.where(
        btc_df['Bitcoin_Price_USD'] > btc_df['Bitcoin_MA_50'], 'BULLISH',
        np.where(btc_df['Bitcoin_Price_USD'] < btc_df['Bitcoin_MA_50'], 'BEARISH', 'NEUTRAL')
    )
    
    # Add volatility regime
    btc_df['Bitcoin_Volatility_Regime'] = np.where(
        btc_df['Bitcoin_30D_Volatility_%'] > 80, 'HIGH_VOLATILITY',
        np.where(btc_df['Bitcoin_30D_Volatility_%'] > 40, 'MEDIUM_VOLATILITY', 'LOW_VOLATILITY')
    )
    
    print(f"   ✅ Bitcoin data fetched: {len(btc_df)} days")
    return btc_df

def create_bitcoin_macro_analysis():
    """Create Bitcoin-focused macro analysis files"""
    
    print("🚀 CREATING BITCOIN-MACRO ANALYSIS FRAMEWORK")
    print("=" * 60)
    
    # Create output directory
    output_dir = 'bitcoin_macro_analysis'
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. Fetch Bitcoin data
    btc_df = fetch_bitcoin_data()
    if btc_df is None:
        return
    
    # 2. Load existing macro data
    print("📊 Loading Macro Economic Data...")
    
    try:
        # Load the simple readable data we created
        macro_dashboard = pd.read_csv('simple_data/economic_dashboard.csv')
        interest_rates = pd.read_csv('simple_data/interest_rates.csv')
        
        print(f"   ✅ Macro data loaded: {len(macro_dashboard)} observations")
    except FileNotFoundError:
        print("   ⚠️  Simple data not found, loading from comprehensive data...")
        # Fallback to comprehensive data
        comprehensive_data = pd.read_csv('exports_comprehensive/macro_indicators_comprehensive.csv')
        
        # Create simplified macro data
        macro_dashboard = pd.DataFrame()
        macro_dashboard['Date'] = pd.to_datetime(comprehensive_data['date']).dt.strftime('%Y-%m-%d')
        macro_dashboard['Fed_Interest_Rate_%'] = comprehensive_data.get('FEDFUNDS')
        macro_dashboard['10_Year_Treasury_%'] = comprehensive_data.get('DGS10')
        macro_dashboard['Unemployment_Rate_%'] = comprehensive_data.get('UNRATE')
        macro_dashboard['S&P_500_Index'] = comprehensive_data.get('SP500')
        macro_dashboard['Market_Fear_Index_VIX'] = comprehensive_data.get('VIXCLS')
        
        interest_rates = macro_dashboard[['Date', 'Fed_Interest_Rate_%', '10_Year_Treasury_%']].copy()
    
    # 3. Merge Bitcoin with Macro Data
    print("🔗 Merging Bitcoin with Macro Data...")
    
    # Convert dates for merging
    btc_df['Date'] = pd.to_datetime(btc_df['Date'])
    macro_dashboard['Date'] = pd.to_datetime(macro_dashboard['Date'])
    
    # Merge on date
    bitcoin_macro = pd.merge(btc_df, macro_dashboard, on='Date', how='left')
    bitcoin_macro = bitcoin_macro.sort_values('Date', ascending=False)
    
    # Fill forward macro data (since macro data is less frequent)
    macro_cols = [col for col in bitcoin_macro.columns if col not in btc_df.columns or col == 'Date']
    for col in macro_cols:
        if col != 'Date':
            bitcoin_macro[col] = bitcoin_macro[col].fillna(method='ffill')
    
    print(f"   ✅ Bitcoin-Macro dataset created: {len(bitcoin_macro)} observations")
    
    # 4. Create Bitcoin-Macro Dashboard
    print("📈 Creating Bitcoin-Macro Dashboard...")
    
    dashboard_cols = [
        'Date', 'Bitcoin_Price_USD', 'Bitcoin_Daily_Change_%', 'Bitcoin_30D_Change_%',
        'Bitcoin_Trend', 'Bitcoin_Volatility_Regime', 'Fed_Interest_Rate_%', 
        '10_Year_Treasury_%', 'Unemployment_Rate_%', 'Market_Fear_Index_VIX'
    ]
    
    dashboard = bitcoin_macro[[col for col in dashboard_cols if col in bitcoin_macro.columns]].copy()
    
    # Add Bitcoin-specific insights
    if 'Fed_Interest_Rate_%' in dashboard.columns and 'Bitcoin_Price_USD' in dashboard.columns:
        # Bitcoin vs Fed Rate relationship
        dashboard['Rate_Environment'] = dashboard['Fed_Interest_Rate_%'].apply(
            lambda x: 'HIGH_RATES' if pd.notna(x) and x > 4 else
                     'MEDIUM_RATES' if pd.notna(x) and x > 2 else
                     'LOW_RATES' if pd.notna(x) else 'UNKNOWN'
        )
    
    # Add Bitcoin market cap estimate (approximate)
    if 'Bitcoin_Price_USD' in dashboard.columns:
        dashboard['Bitcoin_Market_Cap_Billions'] = (dashboard['Bitcoin_Price_USD'] * 19.7).round(1)  # ~19.7M BTC in circulation
    
    dashboard.to_csv(f'{output_dir}/bitcoin_macro_dashboard.csv', index=False)
    print(f"   ✅ Bitcoin-Macro Dashboard -> bitcoin_macro_dashboard.csv ({len(dashboard)} rows)")
    
    # 5. Create Bitcoin vs Interest Rates Analysis
    print("💰 Creating Bitcoin vs Interest Rates Analysis...")
    
    rates_analysis = bitcoin_macro[['Date', 'Bitcoin_Price_USD', 'Bitcoin_30D_Change_%', 
                                   'Fed_Interest_Rate_%', '10_Year_Treasury_%']].copy()
    
    # Add correlation insights
    if len(rates_analysis.dropna()) > 30:
        # Calculate rolling correlations
        rates_analysis['BTC_Fed_Rate_Correlation_30D'] = (
            rates_analysis['Bitcoin_30D_Change_%']
            .rolling(30)
            .corr(rates_analysis['Fed_Interest_Rate_%'])
            .round(3)
        )
    
    rates_analysis.to_csv(f'{output_dir}/bitcoin_vs_interest_rates.csv', index=False)
    print(f"   ✅ Bitcoin vs Interest Rates -> bitcoin_vs_interest_rates.csv ({len(rates_analysis)} rows)")
    
    # 6. Create Bitcoin Volatility vs Macro Uncertainty
    print("📊 Creating Bitcoin Volatility Analysis...")
    
    volatility_cols = ['Date', 'Bitcoin_Price_USD', 'Bitcoin_30D_Volatility_%', 
                      'Bitcoin_Volatility_Regime', 'Market_Fear_Index_VIX']
    
    volatility_analysis = bitcoin_macro[[col for col in volatility_cols if col in bitcoin_macro.columns]].copy()
    
    # Add volatility insights
    if 'Bitcoin_30D_Volatility_%' in volatility_analysis.columns:
        volatility_analysis['Bitcoin_Risk_Level'] = volatility_analysis['Bitcoin_30D_Volatility_%'].apply(
            lambda x: 'EXTREME_RISK' if pd.notna(x) and x > 100 else
                     'HIGH_RISK' if pd.notna(x) and x > 60 else
                     'MEDIUM_RISK' if pd.notna(x) and x > 30 else
                     'LOW_RISK' if pd.notna(x) else 'UNKNOWN'
        )
    
    volatility_analysis.to_csv(f'{output_dir}/bitcoin_volatility_analysis.csv', index=False)
    print(f"   ✅ Bitcoin Volatility Analysis -> bitcoin_volatility_analysis.csv ({len(volatility_analysis)} rows)")
    
    # 7. Create Latest Bitcoin-Macro Snapshot
    print("📸 Creating Latest Bitcoin-Macro Snapshot...")
    
    latest = bitcoin_macro.iloc[0:1].copy()  # Most recent row
    
    # Add interpretations
    if not latest.empty:
        btc_price = latest['Bitcoin_Price_USD'].iloc[0] if 'Bitcoin_Price_USD' in latest.columns else None
        fed_rate = latest['Fed_Interest_Rate_%'].iloc[0] if 'Fed_Interest_Rate_%' in latest.columns else None
        
        snapshot_insights = {
            'Date': latest['Date'].iloc[0].strftime('%Y-%m-%d'),
            'Bitcoin_Price_USD': btc_price,
            'Bitcoin_Market_Cap_Billions': round(btc_price * 19.7, 1) if btc_price else None,
            'Fed_Interest_Rate_%': fed_rate,
            'Rate_Environment': 'HIGH_RATES' if fed_rate and fed_rate > 4 else 'MEDIUM_RATES' if fed_rate and fed_rate > 2 else 'LOW_RATES',
            'Bitcoin_Trend': latest.get('Bitcoin_Trend', {}).iloc[0] if 'Bitcoin_Trend' in latest.columns else 'UNKNOWN',
            'Investment_Thesis': 'Bitcoin as digital gold hedge against monetary policy' if fed_rate and fed_rate > 3 else 'Bitcoin as risk asset in low rate environment'
        }
        
        snapshot_df = pd.DataFrame([snapshot_insights])
        snapshot_df.to_csv(f'{output_dir}/latest_bitcoin_macro_snapshot.csv', index=False)
        print(f"   ✅ Latest Snapshot -> latest_bitcoin_macro_snapshot.csv (1 row)")
    
    # Calculate total size
    total_size = 0
    file_count = 0
    for root, dirs, files in os.walk(output_dir):
        for file in files:
            if file.endswith('.csv'):
                filepath = os.path.join(root, file)
                size = os.path.getsize(filepath)
                total_size += size
                file_count += 1
    
    print("\n" + "=" * 60)
    print("✅ BITCOIN-MACRO ANALYSIS FRAMEWORK CREATED!")
    print("=" * 60)
    print(f"📁 Output directory: {output_dir}/")
    print(f"📊 Files created: {file_count}")
    print(f"💾 Total size: {total_size / (1024*1024):.2f} MB")
    
    print(f"\n📋 BITCOIN-FOCUSED FILES:")
    print(f"  1. bitcoin_macro_dashboard.csv - Main Bitcoin + macro indicators")
    print(f"  2. bitcoin_vs_interest_rates.csv - Bitcoin vs Fed policy analysis")
    print(f"  3. bitcoin_volatility_analysis.csv - Bitcoin risk vs market uncertainty")
    print(f"  4. latest_bitcoin_macro_snapshot.csv - Current Bitcoin-macro snapshot")
    
    print(f"\n🎯 ANALYSIS CAPABILITIES:")
    print(f"  📈 Bitcoin price trends vs macro environment")
    print(f"  💰 Interest rate impact on Bitcoin performance")
    print(f"  📊 Bitcoin volatility vs traditional market fear")
    print(f"  🔍 Real-time Bitcoin-macro relationship tracking")
    
    print(f"\n🚀 READY FOR BITCOIN-MACRO STRATEGY DEVELOPMENT!")
    
    return output_dir

if __name__ == "__main__":
    os.chdir('macro_indicators')
    create_bitcoin_macro_analysis()
