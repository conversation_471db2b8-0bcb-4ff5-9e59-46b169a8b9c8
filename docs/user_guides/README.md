# Bitcoin-Macro Pipeline Documentation

This directory contains documentation for the Bitcoin-Macro Data Pipeline system.

## Main Documentation

- [Bitcoin-Macro Pipeline Guide](../../BITCOIN_MACRO_PIPELINE.md) - Complete system overview
- [Pipeline Cleanup Analysis](../../PIPELINE_CLEANUP_ANALYSIS.md) - Architecture improvements
- [Web Monitor Integration](../../WEB_MONITOR_CLEANUP_PLAN.md) - Source monitoring setup

## Quick Start

1. Install dependencies: `pip install -r requirements.txt`
2. Run test collection: `python run_bitcoin_macro_pipeline.py --mode test`
3. Run daily update: `python run_bitcoin_macro_pipeline.py --mode daily`
4. For full collection: `python run_bitcoin_macro_pipeline.py --mode full --years 2`

## Configuration

- Pipeline settings: `data_pipeline/config/pipeline_config.yaml`
- Data sources: `data_pipeline/config/data_sources.yaml`
- Web monitoring: `Web_Monitor/bitcoin_monitor_config.yaml`

## Output Files

- Unified dataset: `data_pipeline/outputs/bitcoin_macro_analysis.csv`
- Latest snapshot: `data_pipeline/outputs/latest_snapshot.json`

For detailed information, see the main [Bitcoin-Macro Pipeline Guide](../../BITCOIN_MACRO_PIPELINE.md).
