"""
Bitcoin-Macro Data Pipeline Manager

Unified pipeline that orchestrates Bitcoin and macro data collection,
processing, and export in a clean, scalable architecture.
"""

import pandas as pd
import yaml
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from pathlib import Path

from .collectors.bitcoin_collector import BitcoinCollector
from .collectors.macro_collector import MacroCollector

class BitcoinMacroPipeline:
    """Main pipeline manager for Bitcoin-macro data collection and analysis"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        
        # Initialize collectors
        self.bitcoin_collector = BitcoinCollector(self.config.get('bitcoin', {}))
        self.macro_collector = MacroCollector(self.config.get('macro', {}))
        
        # Output directory
        self.output_dir = Path(self.config.get('output_dir', 'data_pipeline/outputs'))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """Load pipeline configuration"""
        if config_path is None:
            config_path = "data_pipeline/config/pipeline_config.yaml"
        
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            # Default configuration
            return {
                'pipeline': {'name': 'bitcoin_macro_pipeline'},
                'collection': {'historical_years': 2},
                'outputs': {
                    'bitcoin_macro_analysis': {'filename': 'bitcoin_macro_analysis.csv'},
                    'latest_snapshot': {'filename': 'latest_snapshot.json'}
                }
            }
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger('bitcoin_macro_pipeline')
    
    def run_full_collection(self, years_back: int = 2) -> Dict[str, Any]:
        """Run complete data collection for specified time period"""
        self.logger.info("🚀 Starting Bitcoin-Macro Data Collection")
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=years_back * 365)
        
        results = {
            'start_time': datetime.now(),
            'date_range': {'start': start_date, 'end': end_date},
            'bitcoin_data': None,
            'macro_data': None,
            'unified_data': None,
            'exports': [],
            'errors': []
        }
        
        try:
            # Collect Bitcoin data
            self.logger.info("📈 Collecting Bitcoin Data...")
            bitcoin_data = self.bitcoin_collector.collect_data(start_date, end_date)
            results['bitcoin_data'] = {
                'rows': len(bitcoin_data),
                'columns': list(bitcoin_data.columns) if not bitcoin_data.empty else [],
                'summary': self.bitcoin_collector.get_bitcoin_metrics_summary(bitcoin_data)
            }
            
            # Collect Macro data
            self.logger.info("📊 Collecting Macro Economic Data...")
            macro_data = self.macro_collector.collect_data(start_date, end_date)
            results['macro_data'] = {
                'rows': len(macro_data),
                'columns': list(macro_data.columns) if not macro_data.empty else [],
                'summary': self.macro_collector.get_macro_summary(macro_data)
            }
            
            # Merge Bitcoin and Macro data
            if not bitcoin_data.empty and not macro_data.empty:
                self.logger.info("🔗 Merging Bitcoin and Macro Data...")
                unified_data = self._merge_bitcoin_macro_data(bitcoin_data, macro_data)
                results['unified_data'] = {
                    'rows': len(unified_data),
                    'columns': list(unified_data.columns)
                }
                
                # Export unified data
                exports = self._export_data(unified_data, bitcoin_data, macro_data)
                results['exports'] = exports
                
            else:
                self.logger.error("❌ Missing data - cannot create unified dataset")
                if bitcoin_data.empty:
                    results['errors'].append("Bitcoin data collection failed")
                if macro_data.empty:
                    results['errors'].append("Macro data collection failed")
            
        except Exception as e:
            self.logger.error(f"❌ Pipeline error: {str(e)}")
            results['errors'].append(str(e))
        
        results['end_time'] = datetime.now()
        results['duration'] = (results['end_time'] - results['start_time']).total_seconds()
        
        self._log_pipeline_summary(results)
        return results
    
    def run_daily_update(self) -> Dict[str, Any]:
        """Run daily data update (last 7 days)"""
        self.logger.info("📅 Running Daily Bitcoin-Macro Update")
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        # Get latest data
        bitcoin_data = self.bitcoin_collector.collect_data(start_date, end_date)
        macro_data = self.macro_collector.collect_data(start_date, end_date)
        
        if not bitcoin_data.empty and not macro_data.empty:
            unified_data = self._merge_bitcoin_macro_data(bitcoin_data, macro_data)
            
            # Export latest snapshot
            snapshot = self._create_latest_snapshot(unified_data)
            snapshot_path = self.output_dir / "latest_snapshot.json"
            
            with open(snapshot_path, 'w') as f:
                import json
                json.dump(snapshot, f, indent=2, default=str)
            
            self.logger.info(f"✅ Daily update complete - snapshot saved to {snapshot_path}")
            return {'status': 'success', 'snapshot_path': str(snapshot_path)}
        
        return {'status': 'failed', 'error': 'Data collection failed'}
    
    def _merge_bitcoin_macro_data(self, bitcoin_data: pd.DataFrame, macro_data: pd.DataFrame) -> pd.DataFrame:
        """Merge Bitcoin and macro data into unified dataset"""
        # Ensure both have date columns
        if 'date' not in bitcoin_data.columns or 'date' not in macro_data.columns:
            self.logger.error("Missing date columns in data")
            return pd.DataFrame()
        
        # Convert dates to datetime for proper merging
        bitcoin_data['date'] = pd.to_datetime(bitcoin_data['date'])
        macro_data['date'] = pd.to_datetime(macro_data['date'])
        
        # Merge on date (left join to keep all Bitcoin data)
        unified = pd.merge(bitcoin_data, macro_data, on='date', how='left')
        
        # Forward fill macro data (since it's less frequent)
        macro_cols = [col for col in macro_data.columns if col != 'date']
        unified[macro_cols] = unified[macro_cols].fillna(method='ffill')
        
        # Sort by date (most recent first)
        unified = unified.sort_values('date', ascending=False)
        
        # Convert date back to string for export
        unified['date'] = unified['date'].dt.strftime('%Y-%m-%d')
        
        return unified
    
    def _export_data(self, unified_data: pd.DataFrame, bitcoin_data: pd.DataFrame, macro_data: pd.DataFrame) -> list:
        """Export data to various formats"""
        exports = []
        
        try:
            # Main unified export
            unified_path = self.output_dir / "bitcoin_macro_analysis.csv"
            unified_data.to_csv(unified_path, index=False)
            exports.append({'file': str(unified_path), 'rows': len(unified_data), 'type': 'unified'})
            
            # Latest snapshot
            snapshot = self._create_latest_snapshot(unified_data)
            snapshot_path = self.output_dir / "latest_snapshot.json"
            
            import json
            with open(snapshot_path, 'w') as f:
                json.dump(snapshot, f, indent=2, default=str)
            exports.append({'file': str(snapshot_path), 'type': 'snapshot'})
            
            self.logger.info(f"✅ Data exported to {len(exports)} files")
            
        except Exception as e:
            self.logger.error(f"❌ Export error: {str(e)}")
        
        return exports
    
    def _create_latest_snapshot(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Create latest data snapshot"""
        if data.empty:
            return {}
        
        latest = data.iloc[0].to_dict()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'date': latest.get('date'),
            'bitcoin': {
                'price_usd': latest.get('bitcoin_price_usd'),
                'market_cap_billions': latest.get('bitcoin_market_cap_billions'),
                'volatility_30d': latest.get('bitcoin_volatility_30d_%'),
                'trend': latest.get('bitcoin_trend'),
                'risk_level': latest.get('bitcoin_risk_level')
            },
            'macro': {
                'fed_funds_rate': latest.get('fed_funds_rate_%'),
                'treasury_10y': latest.get('treasury_10y_%'),
                'unemployment_rate': latest.get('unemployment_rate_%'),
                'sp500_index': latest.get('sp500_index'),
                'vix_index': latest.get('vix_index')
            },
            'analysis': {
                'rate_environment': 'HIGH_RATES' if latest.get('fed_funds_rate_%', 0) > 4 else 'MEDIUM_RATES' if latest.get('fed_funds_rate_%', 0) > 2 else 'LOW_RATES',
                'bitcoin_macro_correlation': 'INVERSE' if latest.get('fed_funds_rate_%', 0) > 3 and latest.get('bitcoin_trend') == 'BEARISH' else 'NEUTRAL'
            }
        }
    
    def _log_pipeline_summary(self, results: Dict[str, Any]):
        """Log pipeline execution summary"""
        self.logger.info("=" * 60)
        self.logger.info("✅ BITCOIN-MACRO PIPELINE COMPLETE")
        self.logger.info("=" * 60)
        self.logger.info(f"⏱️  Duration: {results['duration']:.1f} seconds")
        
        if results['bitcoin_data']:
            self.logger.info(f"📈 Bitcoin Data: {results['bitcoin_data']['rows']} rows")
        
        if results['macro_data']:
            self.logger.info(f"📊 Macro Data: {results['macro_data']['rows']} rows")
        
        if results['unified_data']:
            self.logger.info(f"🔗 Unified Data: {results['unified_data']['rows']} rows")
        
        if results['exports']:
            self.logger.info(f"📁 Files Exported: {len(results['exports'])}")
            for export in results['exports']:
                self.logger.info(f"   - {export['file']}")
        
        if results['errors']:
            self.logger.error(f"❌ Errors: {len(results['errors'])}")
            for error in results['errors']:
                self.logger.error(f"   - {error}")
        
        self.logger.info("🚀 Ready for Bitcoin-Macro Analysis!")
