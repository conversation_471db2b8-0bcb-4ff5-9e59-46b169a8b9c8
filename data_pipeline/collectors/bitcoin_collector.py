"""
Bitcoin Data Collector

Collects Bitcoin price, volume, and market data from multiple sources.
Focuses on Bitcoin-specific metrics and on-chain data.
"""

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any
from .base_collector import BaseCollector

class BitcoinCollector(BaseCollector):
    """Collector for Bitcoin price and market data"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("bitcoin", config)
        self.symbol = "BTC-USD"
        
    def collect_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Collect Bitcoin data for specified date range"""
        start_time = datetime.now()
        self.logger.info(f"Collecting Bitcoin data from {start_date.date()} to {end_date.date()}")
        
        try:
            # Fetch Bitcoin data from Yahoo Finance
            btc_ticker = yf.Ticker(self.symbol)
            btc_data = btc_ticker.history(start=start_date, end=end_date)
            
            if btc_data.empty:
                self.logger.error("No Bitcoin data received from Yahoo Finance")
                return pd.DataFrame()
            
            # Process Bitcoin data
            bitcoin_df = self._process_bitcoin_data(btc_data)
            
            # Clean and validate
            bitcoin_df = self.clean_data(bitcoin_df)
            
            if self.validate_data(bitcoin_df):
                self.log_collection_stats(bitcoin_df, start_time)
                return bitcoin_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Error collecting Bitcoin data: {str(e)}")
            return pd.DataFrame()
    
    def get_latest_data(self) -> pd.DataFrame:
        """Get latest Bitcoin data (last 7 days)"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        return self.collect_data(start_date, end_date)
    
    def _process_bitcoin_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """Process raw Bitcoin data into standardized format"""
        df = pd.DataFrame()
        
        # Basic price data
        df['date'] = raw_data.index.strftime('%Y-%m-%d')
        df['bitcoin_price_usd'] = raw_data['Close'].round(2)
        df['bitcoin_volume'] = raw_data['Volume'].astype(int)
        df['bitcoin_high_usd'] = raw_data['High'].round(2)
        df['bitcoin_low_usd'] = raw_data['Low'].round(2)
        
        # Calculate returns and changes
        df['bitcoin_daily_return_%'] = raw_data['Close'].pct_change() * 100
        df['bitcoin_7d_return_%'] = raw_data['Close'].pct_change(periods=7) * 100
        df['bitcoin_30d_return_%'] = raw_data['Close'].pct_change(periods=30) * 100
        
        # Calculate volatility (30-day rolling)
        df['bitcoin_volatility_30d_%'] = (
            raw_data['Close'].pct_change()
            .rolling(30)
            .std() * np.sqrt(365) * 100
        ).round(2)
        
        # Calculate moving averages
        df['bitcoin_ma_20'] = raw_data['Close'].rolling(20).mean().round(2)
        df['bitcoin_ma_50'] = raw_data['Close'].rolling(50).mean().round(2)
        df['bitcoin_ma_200'] = raw_data['Close'].rolling(200).mean().round(2)
        
        # Add trend indicators
        df['bitcoin_trend'] = np.where(
            df['bitcoin_price_usd'] > df['bitcoin_ma_50'], 'BULLISH',
            np.where(df['bitcoin_price_usd'] < df['bitcoin_ma_50'], 'BEARISH', 'NEUTRAL')
        )
        
        # Add volatility regime
        df['bitcoin_volatility_regime'] = np.where(
            df['bitcoin_volatility_30d_%'] > 80, 'HIGH_VOLATILITY',
            np.where(df['bitcoin_volatility_30d_%'] > 40, 'MEDIUM_VOLATILITY', 'LOW_VOLATILITY')
        )
        
        # Estimate market cap (approximate)
        btc_supply = 19.7  # Million BTC in circulation (approximate)
        df['bitcoin_market_cap_billions'] = (df['bitcoin_price_usd'] * btc_supply).round(1)
        
        # Add risk metrics
        df['bitcoin_risk_level'] = df['bitcoin_volatility_30d_%'].apply(
            lambda x: 'EXTREME_RISK' if pd.notna(x) and x > 100 else
                     'HIGH_RISK' if pd.notna(x) and x > 60 else
                     'MEDIUM_RISK' if pd.notna(x) and x > 30 else
                     'LOW_RISK' if pd.notna(x) else 'UNKNOWN'
        )
        
        return df
    
    def get_required_columns(self) -> List[str]:
        """Return required columns for Bitcoin data"""
        return [
            'date',
            'bitcoin_price_usd',
            'bitcoin_volume',
            'bitcoin_daily_return_%',
            'bitcoin_volatility_30d_%',
            'bitcoin_trend',
            'bitcoin_market_cap_billions'
        ]
    
    def get_bitcoin_metrics_summary(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get summary statistics for Bitcoin data"""
        if data.empty:
            return {}
            
        latest = data.iloc[-1] if len(data) > 0 else {}
        
        return {
            'latest_price': latest.get('bitcoin_price_usd'),
            'latest_market_cap': latest.get('bitcoin_market_cap_billions'),
            'latest_volatility': latest.get('bitcoin_volatility_30d_%'),
            'current_trend': latest.get('bitcoin_trend'),
            'risk_level': latest.get('bitcoin_risk_level'),
            'data_points': len(data),
            'date_range': {
                'start': data['date'].min() if 'date' in data.columns else None,
                'end': data['date'].max() if 'date' in data.columns else None
            }
        }
