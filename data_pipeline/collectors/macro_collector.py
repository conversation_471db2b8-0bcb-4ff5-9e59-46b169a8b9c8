"""
Macro Economic Data Collector

Collects macro economic indicators from FRED API and other sources.
Focuses on key indicators that affect Bitcoin and traditional markets.
"""

import pandas as pd
import requests
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from .base_collector import BaseCollector

class MacroCollector(BaseCollector):
    """Collector for macro economic data from FRED and other sources"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("macro", config)
        self.fred_api_key = config.get('fred_api_key')
        self.fred_base_url = "https://api.stlouisfed.org/fred/series/observations"
        
        # Key macro indicators for Bitcoin analysis
        self.indicators = {
            'FEDFUNDS': 'fed_funds_rate_%',
            'DGS10': 'treasury_10y_%', 
            'DGS2': 'treasury_2y_%',
            'UNRATE': 'unemployment_rate_%',
            'CPIAUCSL': 'cpi_index',
            'CPILFESL': 'core_cpi_index',
            'PAYEMS': 'nonfarm_payrolls_thousands',
            'UMCSENT': 'consumer_sentiment_index'
        }
        
    def collect_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Collect macro economic data for specified date range"""
        start_time = datetime.now()
        self.logger.info(f"Collecting macro data from {start_date.date()} to {end_date.date()}")
        
        all_data = []
        
        try:
            # Collect FRED data
            fred_data = self._collect_fred_data(start_date, end_date)
            if not fred_data.empty:
                all_data.append(fred_data)
            
            # Collect market data from Yahoo Finance
            market_data = self._collect_market_data(start_date, end_date)
            if not market_data.empty:
                all_data.append(market_data)
            
            if not all_data:
                self.logger.error("No macro data collected from any source")
                return pd.DataFrame()
            
            # Merge all data sources
            macro_df = self._merge_data_sources(all_data)
            
            # Clean and validate
            macro_df = self.clean_data(macro_df)
            
            if self.validate_data(macro_df):
                self.log_collection_stats(macro_df, start_time)
                return macro_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Error collecting macro data: {str(e)}")
            return pd.DataFrame()
    
    def get_latest_data(self) -> pd.DataFrame:
        """Get latest macro data (last 30 days)"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        return self.collect_data(start_date, end_date)
    
    def _collect_fred_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Collect data from FRED API"""
        if not self.fred_api_key:
            self.logger.warning("No FRED API key provided, skipping FRED data")
            return pd.DataFrame()
        
        fred_data = {}
        
        for series_id, column_name in self.indicators.items():
            try:
                params = {
                    'series_id': series_id,
                    'api_key': self.fred_api_key,
                    'file_type': 'json',
                    'observation_start': start_date.strftime('%Y-%m-%d'),
                    'observation_end': end_date.strftime('%Y-%m-%d'),
                    'sort_order': 'desc'
                }
                
                response = self.session.get(self.fred_base_url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                observations = data.get('observations', [])
                
                if observations:
                    df = pd.DataFrame(observations)
                    df['date'] = pd.to_datetime(df['date'])
                    df['value'] = pd.to_numeric(df['value'], errors='coerce')
                    df = df.dropna(subset=['value'])
                    
                    if not df.empty:
                        fred_data[column_name] = df.set_index('date')['value']
                        self.logger.info(f"Collected {len(df)} observations for {series_id}")
                
                # Rate limiting
                self.handle_rate_limit(0.5)
                
            except Exception as e:
                self.logger.error(f"Error collecting {series_id}: {str(e)}")
                continue
        
        if fred_data:
            # Combine all FRED series
            fred_df = pd.DataFrame(fred_data)
            fred_df.index.name = 'date'
            fred_df = fred_df.reset_index()
            fred_df['date'] = fred_df['date'].dt.strftime('%Y-%m-%d')
            return fred_df
        
        return pd.DataFrame()
    
    def _collect_market_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Collect market data from Yahoo Finance"""
        try:
            # Get S&P 500 and VIX data
            symbols = ['^GSPC', '^VIX']
            market_data = {}
            
            for symbol in symbols:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(start=start_date, end=end_date)
                
                if not hist.empty:
                    if symbol == '^GSPC':
                        market_data['sp500_index'] = hist['Close']
                    elif symbol == '^VIX':
                        market_data['vix_index'] = hist['Close']
            
            if market_data:
                market_df = pd.DataFrame(market_data)
                market_df.index.name = 'date'
                market_df = market_df.reset_index()
                market_df['date'] = market_df.index.strftime('%Y-%m-%d')
                return market_df
                
        except Exception as e:
            self.logger.error(f"Error collecting market data: {str(e)}")
        
        return pd.DataFrame()
    
    def _merge_data_sources(self, data_sources: List[pd.DataFrame]) -> pd.DataFrame:
        """Merge data from multiple sources"""
        if len(data_sources) == 1:
            return data_sources[0]
        
        # Start with first dataframe
        merged = data_sources[0]
        
        # Merge with remaining dataframes
        for df in data_sources[1:]:
            merged = pd.merge(merged, df, on='date', how='outer')
        
        # Sort by date
        merged = merged.sort_values('date', ascending=False)
        
        # Forward fill missing values (macro data is less frequent)
        numeric_cols = merged.select_dtypes(include=['number']).columns
        merged[numeric_cols] = merged[numeric_cols].fillna(method='ffill')
        
        return merged
    
    def get_required_columns(self) -> List[str]:
        """Return required columns for macro data"""
        return [
            'date',
            'fed_funds_rate_%',
            'treasury_10y_%',
            'unemployment_rate_%',
            'cpi_index'
        ]
    
    def get_macro_summary(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get summary of macro economic conditions"""
        if data.empty:
            return {}
        
        latest = data.iloc[0] if len(data) > 0 else {}
        
        # Determine economic regime
        fed_rate = latest.get('fed_funds_rate_%', 0)
        unemployment = latest.get('unemployment_rate_%', 0)
        
        if fed_rate > 4:
            rate_environment = "HIGH_RATES"
        elif fed_rate > 2:
            rate_environment = "MEDIUM_RATES"
        else:
            rate_environment = "LOW_RATES"
        
        if unemployment < 4:
            employment_condition = "TIGHT_LABOR"
        elif unemployment < 6:
            employment_condition = "NORMAL_LABOR"
        else:
            employment_condition = "LOOSE_LABOR"
        
        return {
            'latest_fed_rate': fed_rate,
            'latest_unemployment': unemployment,
            'rate_environment': rate_environment,
            'employment_condition': employment_condition,
            'data_points': len(data),
            'date_range': {
                'start': data['date'].min() if 'date' in data.columns else None,
                'end': data['date'].max() if 'date' in data.columns else None
            }
        }
