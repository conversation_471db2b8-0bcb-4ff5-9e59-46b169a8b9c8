# 🚀 Bitcoin-Macro Data Pipeline

## 📊 **Overview**

The Bitcoin-Macro Data Pipeline is a comprehensive system for collecting, analyzing, and monitoring Bitcoin price data alongside macro economic indicators. This unified pipeline provides a clean, scalable architecture for understanding the relationship between Bitcoin and the broader economic environment.

## 🏗️ **Architecture**

```
data_pipeline/
├── config/                        # Configuration files
│   ├── pipeline_config.yaml       # Main pipeline settings
│   └── data_sources.yaml          # Data source definitions
├── collectors/                    # Data collection modules
│   ├── base_collector.py          # Abstract base collector
│   ├── bitcoin_collector.py       # Bitcoin price/market data
│   └── macro_collector.py         # Economic indicators
├── pipeline_manager.py            # Main pipeline orchestrator
└── outputs/                       # Data outputs
    ├── bitcoin_macro_analysis.csv # Unified dataset
    └── latest_snapshot.json       # Current conditions
```

## 🔧 **Key Components**

### **1. Data Collectors**

- **BitcoinCollector**: Fetches Bitcoin price, volume, and market data from Yahoo Finance and other sources
- **MacroCollector**: Retrieves economic indicators from FRED API and market indices

### **2. Pipeline Manager**

The `BitcoinMacroPipeline` class orchestrates the entire data flow:
- Initializes collectors based on configuration
- Runs data collection processes
- Merges Bitcoin and macro data
- Exports unified datasets and snapshots

### **3. Web Monitor Integration**

The Web Monitor system (based on changedetection.io) provides:
- Monitoring of data source APIs for outages
- Tracking of economic calendars for new data releases
- Alerts for Bitcoin regulatory changes
- Exchange status monitoring

## 🚀 **Usage**

### **Basic Usage**

```bash
# Run daily update (last 7 days of data)
python run_bitcoin_macro_pipeline.py --mode daily

# Run full historical collection (2 years by default)
python run_bitcoin_macro_pipeline.py --mode full --years 2

# Run test collection (7 days)
python run_bitcoin_macro_pipeline.py --mode test
```

### **With FRED API Key**

```bash
python run_bitcoin_macro_pipeline.py --mode full --fred-api-key YOUR_API_KEY
```

### **Custom Configuration**

```bash
python run_bitcoin_macro_pipeline.py --mode full --config custom_config.yaml
```

## 📊 **Data Sources**

### **Bitcoin Data**
- **Yahoo Finance** (`BTC-USD`): Price, volume, market data
- **Future Integration**: On-chain metrics, exchange flows, derivatives data

### **Macro Economic Data**
- **FRED API**: Federal funds rate, Treasury yields, unemployment, inflation
- **Yahoo Finance**: S&P 500, VIX volatility index
- **Future Integration**: BLS data, Treasury auction results

## 🔍 **Web Monitor Integration**

The Web Monitor system tracks:

### **API Status**
- Bitcoin price APIs (Coinbase, Binance, CoinGecko)
- Economic data APIs (FRED, Yahoo Finance)

### **Economic Calendars**
- Fed meeting schedule
- BLS data release calendar
- Treasury auction schedule

### **Regulatory Monitoring**
- SEC press releases
- CFTC announcements
- Treasury guidance

### **Exchange Status**
- Coinbase, Binance, Kraken status pages

## 📈 **Output Data**

### **Unified Dataset**
The main output is `bitcoin_macro_analysis.csv` containing:
- Bitcoin price, returns, volatility, trend indicators
- Fed funds rate, Treasury yields, unemployment rate
- S&P 500, VIX, and other market indicators
- Merged on date with proper handling of different frequencies

### **Latest Snapshot**
The `latest_snapshot.json` provides current conditions:
- Latest Bitcoin price, trend, and risk level
- Current macro economic indicators
- Rate environment classification
- Bitcoin-macro correlation analysis

## 🔄 **Pipeline Workflow**

1. **Configuration Loading**: Read settings from YAML files
2. **Data Collection**: Fetch Bitcoin and macro data in parallel
3. **Data Validation**: Ensure quality and completeness
4. **Data Merging**: Combine Bitcoin and macro datasets
5. **Analysis**: Calculate derived metrics and relationships
6. **Export**: Save unified dataset and latest snapshot
7. **Monitoring**: Track data sources for changes/outages

## 🛠️ **Customization**

### **Adding New Data Sources**

1. Update `data_sources.yaml` with new source details
2. Extend the appropriate collector class
3. Add new source to the pipeline manager

### **Custom Analysis**

Extend the pipeline manager to add:
- Custom correlation metrics
- Regime classification
- Signal generation
- Predictive models

## 📋 **Requirements**

- Python 3.8+
- pandas, numpy, requests
- yfinance
- PyYAML
- Web Monitor (optional, for source monitoring)

## 🔮 **Future Enhancements**

- **On-chain Metrics**: Add Bitcoin network data (hash rate, active addresses)
- **Sentiment Analysis**: Integrate news and social media sentiment
- **Machine Learning**: Predictive models for Bitcoin based on macro conditions
- **Interactive Dashboard**: Web interface for exploring Bitcoin-macro relationships
- **Alerts System**: Notifications for significant macro-Bitcoin correlations

## 📚 **References**

- [FRED API Documentation](https://fred.stlouisfed.org/docs/api/fred/)
- [Yahoo Finance API](https://pypi.org/project/yfinance/)
- [Web Monitor (changedetection.io)](https://github.com/dgtlmoon/changedetection.io)
