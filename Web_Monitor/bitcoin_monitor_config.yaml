# Bitcoin-Macro Data Source Monitoring Configuration
# Monitors key data sources and alerts when they change or go offline

monitoring_targets:
  
  # Bitcoin Price APIs
  bitcoin_apis:
    - name: "Coinbase API Status"
      url: "https://api.coinbase.com/v2/exchange-rates"
      check_frequency: "5min"
      alert_on_failure: true
      expected_content: "BTC"
      
    - name: "Binance API Status"  
      url: "https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT"
      check_frequency: "5min"
      alert_on_failure: true
      expected_content: "BTCUSDT"
      
    - name: "CoinGecko API Status"
      url: "https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd"
      check_frequency: "10min"
      alert_on_failure: true
      expected_content: "bitcoin"

  # Economic Data Sources
  economic_apis:
    - name: "FRED API Status"
      url: "https://api.stlouisfed.org/fred/series?series_id=FEDFUNDS&api_key=demo&file_type=json"
      check_frequency: "30min"
      alert_on_failure: true
      expected_content: "FEDFUNDS"
      
    - name: "Yahoo Finance Status"
      url: "https://query1.finance.yahoo.com/v8/finance/chart/^GSPC"
      check_frequency: "15min"
      alert_on_failure: true
      expected_content: "chart"

  # Federal Reserve Monitoring (Primary Focus)
  federal_reserve:
    - name: "Fed Recent Developments"
      url: "https://www.federalreserve.gov/"
      check_frequency: "hourly"
      alert_on_change: true
      css_selector: "h2:contains('Recent Developments') + ul, h2:contains('Recent Developments') + div"
      description: "Monitor Fed homepage for new speeches, announcements, and policy updates"

    - name: "Fed Chair Powell Speeches"
      url: "https://www.federalreserve.gov/newsevents/speech/powell.htm"
      check_frequency: "daily"
      alert_on_change: true
      css_selector: ".eventlist"
      description: "Track all Chair Powell speeches and remarks"

    - name: "Fed Press Releases"
      url: "https://www.federalreserve.gov/newsevents/pressreleases.htm"
      check_frequency: "hourly"
      alert_on_change: true
      css_selector: ".eventlist, .press-release-list"
      description: "Monitor Fed press releases for policy announcements"

    - name: "FOMC Statements"
      url: "https://www.federalreserve.gov/newsevents/pressreleases/monetary.htm"
      check_frequency: "daily"
      alert_on_change: true
      css_selector: ".eventlist"
      description: "Track FOMC meeting statements and monetary policy decisions"

    - name: "Fed Economic Projections"
      url: "https://www.federalreserve.gov/monetarypolicy/fomcprojtabl.htm"
      check_frequency: "weekly"
      alert_on_change: true
      css_selector: ".data, .projection-table"
      description: "Monitor updates to Fed economic projections and dot plot"

  # Economic Calendar Monitoring
  economic_calendars:
    - name: "Fed Meeting Calendar"
      url: "https://www.federalreserve.gov/newsevents/calendar.htm"
      check_frequency: "daily"
      alert_on_change: true
      css_selector: ".calendar-table"

    - name: "BLS Employment Schedule"
      url: "https://www.bls.gov/schedule/news_release/empsit.htm"
      check_frequency: "weekly"
      alert_on_change: true

  # Bitcoin Regulatory Monitoring
  regulatory_sources:
    - name: "SEC Bitcoin Press Releases"
      url: "https://www.sec.gov/news/pressreleases"
      check_frequency: "hourly"
      alert_on_change: true
      filter_keywords: ["bitcoin", "cryptocurrency", "digital asset", "crypto"]
      
    - name: "CFTC Crypto Announcements"
      url: "https://www.cftc.gov/PressRoom/PressReleases/index.htm"
      check_frequency: "hourly"
      alert_on_change: true
      filter_keywords: ["bitcoin", "cryptocurrency", "digital asset"]
      
    - name: "Treasury Crypto Guidance"
      url: "https://home.treasury.gov/news/press-releases"
      check_frequency: "daily"
      alert_on_change: true
      filter_keywords: ["cryptocurrency", "digital asset", "bitcoin"]

  # Exchange Status Pages
  exchange_status:
    - name: "Coinbase Status"
      url: "https://status.coinbase.com/"
      check_frequency: "5min"
      alert_on_change: true
      css_selector: ".status-indicator"
      
    - name: "Binance Status"
      url: "https://status.binance.com/"
      check_frequency: "5min"
      alert_on_change: true
      
    - name: "Kraken Status"
      url: "https://status.kraken.com/"
      check_frequency: "5min"
      alert_on_change: true

# Alert Configuration
alerts:
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ""
    password: ""
    to_addresses: []
    
  webhook:
    enabled: true
    url: "http://localhost:8000/webhook/bitcoin-monitor"
    method: "POST"
    headers:
      Content-Type: "application/json"
    
  log_file:
    enabled: true
    path: "logs/bitcoin_monitor.log"
    max_size_mb: 10

# Pipeline Integration
pipeline_integration:
  trigger_data_collection: true
  webhook_endpoint: "http://localhost:8000/trigger/data-collection"
  
  # Trigger conditions
  triggers:
    - condition: "api_failure"
      action: "switch_to_backup_source"
      
    - condition: "new_economic_data"
      action: "run_daily_update"
      
    - condition: "regulatory_change"
      action: "log_event_and_notify"

# Monitoring Schedule
schedule:
  high_frequency: "*/5 * * * *"    # Every 5 minutes (APIs, exchange status)
  medium_frequency: "*/15 * * * *"  # Every 15 minutes (market data)
  low_frequency: "0 */6 * * *"      # Every 6 hours (regulatory, calendars)
  daily: "0 9 * * *"                # Daily at 9 AM (comprehensive check)
