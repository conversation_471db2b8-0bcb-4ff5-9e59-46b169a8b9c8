#!/usr/bin/env python3
"""
Debug script to inspect message structure in WhatsApp
"""

import subprocess
import sys
import json

def debug_message_structure(group_name):
    """Click on a group and debug the message structure"""
    script = f'''
import json
from playwright.sync_api import sync_playwright
import time

try:
    with sync_playwright() as p:
        browser = p.chromium.connect_over_cdp("http://localhost:9222")
        context = browser.contexts[0]
        pages = context.pages
        
        whatsapp_page = None
        for page in pages:
            if 'web.whatsapp.com' in page.url:
                whatsapp_page = page
                break
        
        if not whatsapp_page:
            result = {{'success': False, 'error': 'WhatsApp page not found'}}
        else:
            # Get chat elements
            chat_elements = whatsapp_page.query_selector_all('#pane-side div[role="listitem"]')
            
            # Find target group
            target_index = -1
            for i, chat_elem in enumerate(chat_elements):
                try:
                    title_elem = chat_elem.query_selector('span[title]')
                    if title_elem:
                        title = title_elem.get_attribute('title') or title_elem.text_content()
                        if title and title.strip() == "{group_name}":
                            target_index = i
                            break
                except:
                    continue
            
            if target_index == -1:
                result = {{'success': False, 'error': 'Group not found'}}
            else:
                # Click the group
                chat_elements[target_index].click()
                time.sleep(5)  # Wait longer for messages to load
                
                # Debug: Check what elements exist in the conversation area
                debug_info = {{}}
                
                # Check main conversation area
                main_area = whatsapp_page.query_selector('#main')
                if main_area:
                    debug_info['main_area_found'] = True
                    
                    # Try to find message containers with various selectors
                    selectors_to_try = [
                        'div[data-testid="msg-container"]',
                        'div[data-testid="conversation-panel-messages"]',
                        'div[role="row"]',
                        'div[class*="message"]',
                        'div[class*="msg"]',
                        'span[data-testid="msg-text"]',
                        'div[data-testid="msg-text"]',
                        '.selectable-text',
                        'span.selectable-text'
                    ]
                    
                    debug_info['selector_results'] = {{}}
                    
                    for selector in selectors_to_try:
                        elements = whatsapp_page.query_selector_all(selector)
                        debug_info['selector_results'][selector] = len(elements)
                        
                        # If we found elements, get sample content
                        if elements and len(elements) > 0:
                            sample_texts = []
                            for elem in elements[:3]:  # First 3 elements
                                try:
                                    text = elem.text_content()
                                    if text and text.strip():
                                        sample_texts.append(text.strip()[:100])  # First 100 chars
                                except:
                                    sample_texts.append("Error getting text")
                            debug_info['selector_results'][selector + '_samples'] = sample_texts
                    
                    # Try to get the HTML structure of the conversation area
                    try:
                        conv_html = main_area.inner_html()
                        # Get first 2000 characters to avoid too much output
                        debug_info['conversation_html_sample'] = conv_html[:2000] if conv_html else "No HTML content"
                    except:
                        debug_info['conversation_html_sample'] = "Error getting HTML"
                        
                else:
                    debug_info['main_area_found'] = False
                
                result = {{'success': True, 'debug_info': debug_info}}
        
        print(json.dumps(result, indent=2))

except Exception as e:
    print(json.dumps({{'success': False, 'error': str(e)}}))
'''
    
    try:
        # Write script to temp file
        with open('/tmp/debug_script.py', 'w') as f:
            f.write(script)
        
        # Run in separate process
        result = subprocess.run([sys.executable, '/tmp/debug_script.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            return {'success': False, 'error': result.stderr}
    except Exception as e:
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # Test with the first group that has messages
    group_name = "Daniel Tan"  # Current available group
    
    print(f"🔍 Debugging message structure for group: {group_name}")
    print("=" * 60)
    
    result = debug_message_structure(group_name)
    
    if result['success']:
        debug_info = result['debug_info']
        
        print(f"✅ Main area found: {debug_info.get('main_area_found', False)}")
        print("\n📊 Selector Results:")
        
        selector_results = debug_info.get('selector_results', {})
        for selector, count in selector_results.items():
            if not selector.endswith('_samples'):
                print(f"  {selector}: {count} elements")
                
                # Show samples if available
                samples_key = selector + '_samples'
                if samples_key in selector_results:
                    samples = selector_results[samples_key]
                    if samples:
                        print(f"    Samples: {samples}")
        
        print(f"\n📄 HTML Sample:")
        html_sample = debug_info.get('conversation_html_sample', 'No HTML available')
        print(html_sample)
        
    else:
        print(f"❌ Error: {result['error']}")
