#!/usr/bin/env python3
"""
Debug script to see what HTML we're getting from WhatsApp Web
"""

import json
from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup

def debug_whatsapp_html():
    """Debug WhatsApp HTML structure"""
    try:
        with sync_playwright() as p:
            # Connect to existing Chrome instance
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            
            # Get the WhatsApp page
            pages = browser.contexts[0].pages
            whatsapp_page = None
            
            for page in pages:
                if 'web.whatsapp.com' in page.url:
                    whatsapp_page = page
                    break
            
            if not whatsapp_page:
                print("❌ WhatsApp Web tab not found")
                return
            
            print(f"✅ Found WhatsApp tab: {whatsapp_page.title()}")
            print(f"🔗 URL: {whatsapp_page.url}")
            
            # Scroll to make sure messages are loaded
            print("📜 Scrolling to load messages...")
            whatsapp_page.evaluate("""
                const messagesContainer = document.querySelector('[data-testid="conversation-panel-messages"]');
                if (messagesContainer) {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
            """)
            
            # Wait a bit
            whatsapp_page.wait_for_timeout(2000)
            
            # Get page content
            html_content = whatsapp_page.content()
            
            # Save full HTML for debugging
            with open('debug_whatsapp_full.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print("💾 Saved full HTML to debug_whatsapp_full.html")
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for different message selectors
            selectors_to_try = [
                '[data-testid="msg-container"]',
                '[data-testid="conversation-panel-messages"] div',
                '.message-in, .message-out',
                '[data-id]',
                'div[class*="message"]',
                'span[dir="ltr"]'
            ]
            
            print("\n🔍 Testing different selectors:")
            for selector in selectors_to_try:
                elements = soup.select(selector)
                print(f"  {selector}: {len(elements)} elements found")
                
                if elements and len(elements) > 0:
                    print(f"    Sample element: {str(elements[0])[:200]}...")
            
            # Look for conversation panel
            conv_panel = soup.select('[data-testid="conversation-panel-messages"]')
            if conv_panel:
                print(f"\n📱 Found conversation panel: {len(conv_panel)} elements")
                
                # Save just the conversation panel
                with open('debug_conversation_panel.html', 'w', encoding='utf-8') as f:
                    f.write(str(conv_panel[0]))
                print("💾 Saved conversation panel to debug_conversation_panel.html")
                
                # Look for any divs with text content
                text_elements = conv_panel[0].find_all(text=True)
                text_content = [t.strip() for t in text_elements if t.strip()]
                
                print(f"\n📝 Found {len(text_content)} text elements:")
                for i, text in enumerate(text_content[:10]):  # Show first 10
                    if len(text) > 5:  # Only show meaningful text
                        print(f"  {i+1}. {text[:100]}")
            
            # Check if we're on the right page
            page_title = whatsapp_page.title()
            if "WhatsApp" not in page_title:
                print(f"⚠️  Page title doesn't contain 'WhatsApp': {page_title}")
            
            # Check for login/QR code
            qr_code = soup.select('[data-testid="qr-code"]')
            if qr_code:
                print("⚠️  QR code detected - please log in to WhatsApp Web")
            
            # Check for chat list
            chat_list = soup.select('[data-testid="chat-list"]')
            if chat_list:
                print("✅ Chat list found - logged in successfully")
            else:
                print("❌ Chat list not found - may not be logged in")
            
            return html_content
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🐛 Debugging WhatsApp HTML Structure")
    print("=" * 50)
    
    result = debug_whatsapp_html()
    
    if result:
        print("\n✅ Debug complete!")
        print("📁 Check debug_whatsapp_full.html and debug_conversation_panel.html")
        print("🔍 Review the selector results above to find the right message elements")
    else:
        print("\n❌ Debug failed")
