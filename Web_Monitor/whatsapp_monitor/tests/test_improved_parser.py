#!/usr/bin/env python3
"""
Test the improved WhatsApp parser
"""

import json
from playwright.sync_api import sync_playwright
from whatsapp_parser import WhatsAppMessageParser
from whatsapp_integration import save_messages_locally

def test_improved_parser():
    """Test the improved parser with real WhatsApp content"""
    try:
        with sync_playwright() as p:
            # Connect to existing Chrome
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            
            # Create a new page and navigate to WhatsApp
            context = browser.contexts[0]
            page = context.new_page()
            
            print("🔄 Navigating to WhatsApp Web...")
            page.goto("https://web.whatsapp.com/", wait_until="networkidle")
            
            # Wait for page to load
            print("⏳ Waiting for page to load...")
            page.wait_for_timeout(3000)
            
            # Scroll to load recent messages
            print("📜 Scrolling to load messages...")
            page.evaluate("""
                const messagesContainer = document.querySelector('[data-testid="conversation-panel-messages"]');
                if (messagesContainer) {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
            """)
            
            page.wait_for_timeout(2000)
            
            # Get page content
            print("📄 Getting page content...")
            html_content = page.content()
            
            # Use our improved parser
            print("🔍 Parsing messages with improved parser...")
            parser = WhatsAppMessageParser()
            messages = parser.parse_messages(html_content)
            
            if messages:
                print(f"✅ Found {len(messages)} messages:")
                for i, msg in enumerate(messages, 1):
                    print(f"  {i}. {msg}")
                
                # Save locally using our integration function
                filename = save_messages_locally(messages, "test_group")
                print(f"💾 Saved to {filename}")
                
                # Also create the pipeline payload
                payload = parser.create_pipeline_payload(messages)
                
                with open('parsed_messages.json', 'w', encoding='utf-8') as f:
                    json.dump(payload, f, indent=2, ensure_ascii=False)
                
                print("📁 Also saved pipeline format to parsed_messages.json")
                
                return payload
            else:
                print("❌ No messages found")
                
                # Debug: save HTML for inspection
                with open('debug_no_messages.html', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print("🐛 Saved HTML to debug_no_messages.html for inspection")
                
                return None
            
            page.close()
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_with_existing_html():
    """Test parser with previously saved HTML"""
    try:
        print("📄 Testing with previously saved HTML...")
        
        # Try to load the saved HTML
        try:
            with open('whatsapp_page.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
        except FileNotFoundError:
            print("❌ No saved HTML found. Run simple_test.py first.")
            return None
        
        # Parse with improved parser
        parser = WhatsAppMessageParser()
        messages = parser.parse_messages(html_content)
        
        if messages:
            print(f"✅ Found {len(messages)} messages from saved HTML:")
            for i, msg in enumerate(messages, 1):
                print(f"  {i}. {msg}")
            
            return messages
        else:
            print("❌ No messages found in saved HTML")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    print("🧪 Testing Improved WhatsApp Parser")
    print("=" * 40)
    
    # First try with existing HTML
    print("1. Testing with saved HTML...")
    result1 = test_with_existing_html()
    
    print("\n" + "="*40)
    
    # Then try with fresh content
    print("2. Testing with fresh WhatsApp content...")
    result2 = test_improved_parser()
    
    if result1 or result2:
        print("\n🎉 Parser test successful!")
        if result2:
            print(f"📊 Latest test found {result2['count']} messages")
    else:
        print("\n⚠️  No messages found in either test")
        print("💡 Make sure you're in a WhatsApp group with recent messages")
