#!/usr/bin/env python3
"""
Bitcoin-Macro Data Pipeline Runner

Simple script to run the unified Bitcoin-macro data collection and analysis pipeline.
This replaces all the scattered scripts with a single, clean interface.
"""

import sys
import argparse
from datetime import datetime
from pathlib import Path

# Add data_pipeline to path
sys.path.append(str(Path(__file__).parent))

from data_pipeline.pipeline_manager import BitcoinMacroPipeline

def main():
    parser = argparse.ArgumentParser(description='Bitcoin-Macro Data Pipeline')
    parser.add_argument('--mode', choices=['full', 'daily', 'test'], default='daily',
                       help='Pipeline mode: full collection, daily update, or test run')
    parser.add_argument('--years', type=int, default=2,
                       help='Years of historical data for full collection (default: 2)')
    parser.add_argument('--config', type=str,
                       help='Path to configuration file')
    parser.add_argument('--fred-api-key', type=str,
                       help='FRED API key for macro data collection')
    
    args = parser.parse_args()
    
    print("🚀 Bitcoin-Macro Data Pipeline")
    print("=" * 50)
    
    try:
        # Initialize pipeline
        pipeline = BitcoinMacroPipeline(config_path=args.config)
        
        # Add FRED API key if provided
        if args.fred_api_key:
            pipeline.config['macro'] = pipeline.config.get('macro', {})
            pipeline.config['macro']['fred_api_key'] = args.fred_api_key
            pipeline.macro_collector.fred_api_key = args.fred_api_key
        
        # Run pipeline based on mode
        if args.mode == 'full':
            print(f"📊 Running full collection ({args.years} years of data)")
            results = pipeline.run_full_collection(years_back=args.years)
            
        elif args.mode == 'daily':
            print("📅 Running daily update")
            results = pipeline.run_daily_update()
            
        elif args.mode == 'test':
            print("🧪 Running test collection (7 days)")
            results = pipeline.run_full_collection(years_back=0.02)  # ~7 days
        
        # Print results summary
        print("\n" + "=" * 50)
        print("✅ PIPELINE COMPLETE")
        print("=" * 50)
        
        if isinstance(results, dict):
            if 'duration' in results:
                print(f"⏱️  Duration: {results['duration']:.1f} seconds")
            
            if 'exports' in results and results['exports']:
                print(f"📁 Files created:")
                for export in results['exports']:
                    print(f"   - {export['file']}")
                    if 'rows' in export:
                        print(f"     ({export['rows']} rows)")
            
            if 'errors' in results and results['errors']:
                print(f"❌ Errors encountered:")
                for error in results['errors']:
                    print(f"   - {error}")
            
            # Show latest snapshot if available
            if args.mode in ['daily', 'test']:
                snapshot_path = Path("data_pipeline/outputs/latest_snapshot.json")
                if snapshot_path.exists():
                    print(f"\n📸 Latest snapshot: {snapshot_path}")
                    
                    import json
                    with open(snapshot_path) as f:
                        snapshot = json.load(f)
                    
                    if 'bitcoin' in snapshot:
                        btc = snapshot['bitcoin']
                        print(f"   Bitcoin: ${btc.get('price_usd', 'N/A'):,.2f}")
                        print(f"   Trend: {btc.get('trend', 'N/A')}")
                    
                    if 'macro' in snapshot:
                        macro = snapshot['macro']
                        print(f"   Fed Rate: {macro.get('fed_funds_rate', 'N/A')}%")
                        print(f"   10Y Treasury: {macro.get('treasury_10y', 'N/A')}%")
        
        print("\n🎯 Next Steps:")
        print("   1. Review data in data_pipeline/outputs/")
        print("   2. Use bitcoin_macro_analysis.csv for analysis")
        print("   3. Check latest_snapshot.json for current conditions")
        print("   4. Set up daily automation with --mode daily")
        
    except Exception as e:
        print(f"❌ Pipeline failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
